services:
  db:
    image: pgvector/pgvector:pg15
    environment:
      POSTGRES_DB: aggie_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  backend-api:
    build:
      context: ./apps/backend-api
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/aggie_dev
      - REDIS_URL=redis://redis:6379/0
      - JWT_SECRET_KEY=dev-secret-key-change-in-production
      - ENCRYPTION_KEY=dev-encryption-key-32-chars-long
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LLM_PROVIDER=openai
      - ENVIRONMENT=development
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./apps/backend-api:/app
      - ./uploads:/app/uploads
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  worker:
    build:
      context: ./apps/backend-api
      dockerfile: Dockerfile
    environment:
      - DATABASE_URL=**************************************/aggie_dev
      - REDIS_URL=redis://redis:6379/0
      - JWT_SECRET_KEY=dev-secret-key-change-in-production
      - ENCRYPTION_KEY=dev-encryption-key-32-chars-long
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LLM_PROVIDER=openai
      - ENVIRONMENT=development
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./apps/backend-api:/app
      - ./uploads:/app/uploads
    command: celery -A app.celery_app worker --loglevel=info

  scheduler:
    build:
      context: ./apps/backend-api
      dockerfile: Dockerfile
    user: root
    environment:
      - DATABASE_URL=**************************************/aggie_dev
      - REDIS_URL=redis://redis:6379/0
      - JWT_SECRET_KEY=dev-secret-key-change-in-production
      - ENCRYPTION_KEY=dev-encryption-key-32-chars-long
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LLM_PROVIDER=openai
      - ENVIRONMENT=development
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./apps/backend-api:/app
      - celery_data:/app/celery
    command: celery -A app.celery_app beat --loglevel=info --schedule=/app/celery/celerybeat-schedule

  admin-frontend:
    build:
      context: .
      dockerfile: apps/admin-frontend/Dockerfile
    environment:
      - REACT_APP_API_URL=http://localhost:3001/api
      - PORT=3001
    volumes:
      - ./apps/admin-frontend/src:/app/apps/admin-frontend/src
      - ./packages:/app/packages
      - ./packages/shared-types:/app/apps/admin-frontend/node_modules/@aggie/shared-types
      - ./packages/ui-components:/app/apps/admin-frontend/node_modules/@aggie/ui-components
      - /app/node_modules
      - /app/apps/admin-frontend/node_modules

  app-frontend:
    build:
      context: .
      dockerfile: apps/app-frontend/Dockerfile
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - PORT=3002
    volumes:
      - ./apps/app-frontend/src:/app/apps/app-frontend/src
      - ./packages:/app/packages
      - ./packages/shared-types:/app/apps/app-frontend/node_modules/@aggie/shared-types
      - ./packages/ui-components:/app/apps/app-frontend/node_modules/@aggie/ui-components
      - /app/node_modules
      - /app/apps/app-frontend/node_modules

  reverse-proxy:
    image: nginx:alpine
    ports:
      - "80:80"
      - "3001:3001"  # Admin console (console.aggi.se)
      - "3002:3002"  # User app (app.aggi.se)
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - backend-api
      - admin-frontend
      - app-frontend

volumes:
  postgres_data:
  celery_data:
